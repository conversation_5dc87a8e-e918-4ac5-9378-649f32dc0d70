#[allow(unused_imports)]
use std::hash::Hasher;

use protobuf::ProtobufEnum;
use reactive_state::middleware::{Middleware, NotifyFn, ReduceFn, ReduceMiddlewareResult};

use pianorhythm_proto::midi_renditions::UpdateChannelPayload;
use pianorhythm_proto::pianorhythm_actions::{AppStateActions, AppStateActions_Action, AudioSynthActions, AudioSynthActions_Action, SocketIdWithInt32};
use pianorhythm_proto::pianorhythm_effects::{AppStateEffects, AppStateEffects_Action};
use pianorhythm_proto::pianorhythm_events::AppStateEvents;

use crate::reducers::app_state::AppState;
use crate::utils::{create_audio_synth_action, hash_socket_id, SocketId};

pub struct HandleSynthActionsMiddleware<'c> {
    pub core_api: &'c crate::types::CoreClientApiType,
}

impl<'c> Middleware<AppState, AppStateActions, AppStateEvents, AppStateEffects> for HandleSynthActionsMiddleware<'c> {
    fn on_reduce(
        &self, store: &crate::Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>, action: Option<&AppStateActions>,
        reduce: ReduceFn<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
    ) -> ReduceMiddlewareResult<AppStateEvents, AppStateEffects> {
        if let Some(action) = action {
            let current_state = &store.state().audio_process_state;
            let client_socket_id = current_state.client_socket_id_hashed;

            let add_user = |socket_id: u32| {
                #[cfg(feature = "use_synth")]
                pianorhythm_synth::add_socket(socket_id, client_socket_id.unwrap_or_default() == socket_id);
            };

            #[cfg(feature = "use_synth")]
            match action.action {
                AppStateActions_Action::SynthAction if action.has_audioSynthAction() => {
                    let synth_action = action.get_audioSynthAction();

                    let add_synth_user = |socket_id: &str, is_client: bool| {
                        let hash_value = pianorhythm_shared::util::hash_string_to_u32(socket_id.to_string());

                        if pianorhythm_synth::add_socket(hash_value, is_client) {
                            log::info!("[add_synth_user] Added socket {hash_value} to synth.");
                            let mut _action = AppStateActions::new();
                            _action.set_action(AppStateActions_Action::AddHashedSynthUser);

                            let mut input = SocketIdWithInt32::new();
                            input.set_int32Value(hash_value);
                            input.set_socketId(socket_id.to_string());
                            _action.set_socketIdWithIn32(input);
                            store.dispatch(_action);
                            return Some(hash_value);
                        }

                        None
                    };

                    match synth_action.action {
                        AudioSynthActions_Action::NoteOn if synth_action.has_sourceSocketID() && synth_action.has_synthData() => {
                            let socket_id = synth_action.get_sourceSocketID();
                            let hash_value = pianorhythm_shared::util::hash_string_to_u32(socket_id.to_string());
                            let data = synth_action.get_synthData();
                            pianorhythm_synth::note_on(data.channel as u8, data.note as u8, data.velocity as u8, Some(hash_value), data.get_noteSource().value() as u8);
                        }
                        AudioSynthActions_Action::NoteOff if synth_action.has_sourceSocketID() && synth_action.has_synthData() => {
                            let socket_id = synth_action.get_sourceSocketID();
                            let hash_value = pianorhythm_shared::util::hash_string_to_u32(socket_id.to_string());
                            let data = synth_action.get_synthData();
                            pianorhythm_synth::note_off(data.channel as u8, data.note as u8, Some(hash_value), data.get_noteSource().value() as u8);
                        }
                        AudioSynthActions_Action::AddUser if synth_action.has_socketId() => {
                            _ = add_synth_user(synth_action.get_socketId(), false);
                        }
                        AudioSynthActions_Action::AddClient if synth_action.has_socketId() => {
                            let socket_id_str = synth_action.get_socketId();
                            log::info!("Adding client to synth with socket ID: {}", socket_id_str);

                            if let Some(socket_id) = add_synth_user(socket_id_str, true) {
                                pianorhythm_synth::set_client_socket_id(socket_id);
                                log::info!("Successfully added client to synth and set client socket ID: {}", socket_id);
                            } else {
                                log::error!("Failed to add client to synth for socket ID: {}", socket_id_str);
                            }
                        }
                        AudioSynthActions_Action::RemoveUser if synth_action.has_socketId() => {
                            let _socket_id = synth_action.get_socketId();
                            if let Some(socket_id) = store
                                .state()
                                .audio_process_state
                                .hashed_socket_ids
                                .get(&SocketId::from(_socket_id.to_string()))
                            {
                                pianorhythm_synth::remove_socket(socket_id.clone());

                                let mut _action = AppStateActions::new();
                                _action.set_action(AppStateActions_Action::RemoveHashedSynthUser);
                                _action.set_socketId(_socket_id.to_string());
                                store.dispatch(_action);
                            }
                        }
                        AudioSynthActions_Action::SetChannelVolume if synth_action.has_channelWithUint32() => {
                            let payload = synth_action.get_channelWithUint32();
                            store.dispatch(pianorhythm_shared::util::create_action_with(AppStateActions_Action::UpdateChannelParameter, |action| {
                                let mut update = UpdateChannelPayload::new();
                                update.set_channel(payload.get_channel());
                                update.set_volume(payload.get_uint32Value());
                                action.set_updateChannelPayload(update);
                            }));
                        }
                        AudioSynthActions_Action::SetChannelPan if synth_action.has_channelWithUint32() => {
                            let payload = synth_action.get_channelWithUint32();
                            store.dispatch(pianorhythm_shared::util::create_action_with(AppStateActions_Action::UpdateChannelParameter, |action| {
                                let mut update = UpdateChannelPayload::new();
                                update.set_channel(payload.get_channel());
                                update.set_pan(payload.get_uint32Value());
                                action.set_updateChannelPayload(update);
                            }));
                        }
                        AudioSynthActions_Action::SetChannelExpression if synth_action.has_channelWithUint32() => {}
                        _ => {}
                    }
                }
                AppStateActions_Action::JoinedRoom if action.has_joinedRoomData() => {
                    pianorhythm_synth::clear_all_users_except_client();
                }
                AppStateActions_Action::SetUsers if action.has_userDtoList() => {
                    for user in action.get_userDtoList().get_userDto().iter() {
                        if let Some(socket_id) = hash_socket_id(user.get_socketID()) {
                            add_user(socket_id);
                        }
                    }
                }
                AppStateActions_Action::AddUser if action.has_userDto() => {
                    if let Some(socket_id) = hash_socket_id(action.get_userDto().get_socketID()) {
                        add_user(socket_id);
                    }
                }
                AppStateActions_Action::UpdateUser => {}
                AppStateActions_Action::RemoveUser if action.has_socketId() => {
                    if let Some(socket_id) = hash_socket_id(action.get_socketId()) {
                        pianorhythm_synth::remove_socket(socket_id);
                    }
                }
                AppStateActions_Action::SetUserVolume if action.has_sourceSocketID() && action.has_uint32Value() => {
                    if let Some(socket_id) = hash_socket_id(action.get_sourceSocketID()) {
                        pianorhythm_synth::set_user_volume(action.get_uint32Value(), Some(socket_id));
                    }
                }
                AppStateActions_Action::SetUserVelocityPercentage if action.has_sourceSocketID() && action.has_uint32Value() => {
                    if let Some(socket_id) = hash_socket_id(action.get_sourceSocketID()) {
                        pianorhythm_synth::set_user_velocity_percentage(action.get_uint32Value(), Some(socket_id));
                    }
                }
                AppStateActions_Action::ToggleChannelActive if action.has_uint32Value() => {
                    let target_channel = action.get_uint32Value() as usize;
                    pianorhythm_synth::get_audio_channel(target_channel as u8, None).inspect(|channel| {
                        pianorhythm_synth::set_channel_active(target_channel as u8, !channel.get_active(), client_socket_id);
                    });
                }
                AppStateActions_Action::ClearAllAudioChannels => {
                    pianorhythm_synth::client_clear_all_audio_channels();
                }
                AppStateActions_Action::SynthEngineCreated if store.state().client_state.socket_id.is_some() => {
                    let mut ac = AudioSynthActions::new();
                    ac.set_action(AudioSynthActions_Action::AddClient);
                    ac.set_socketId(store.state().client_state.socket_id.clone().unwrap_or_default().to_string());
                    store.dispatch(create_audio_synth_action(ac));
                }
                AppStateActions_Action::SynthEngineCreated => {
                    if client_socket_id.is_none() {
                        log::warn!("Synth engine created but client socket ID not set yet. This may indicate a race condition in initialization order.");
                        // The synth engine is ready, but we need to wait for the client socket ID
                        // This will be handled when the AddClient action is received later
                    } else {
                        log::info!("Synth engine created with client socket ID: {:?}", client_socket_id);
                    }
                }
                AppStateActions_Action::ResetAudioChannelsToDefault => {
                    pianorhythm_synth::client_reset_all_controllers();
                    pianorhythm_synth::client_reset_channels_to_default();
                }
                AppStateActions_Action::SetInstrumentOnChannel if action.has_setChannelInstrumentPayload() => {
                    pianorhythm_synth::client_set_instrument_on_channel(action.get_setChannelInstrumentPayload());
                }
                AppStateActions_Action::RemoveInstrumentFromChannel if action.has_uint32Value() => {
                    pianorhythm_synth::clear_program_on_channel(action.get_uint32Value() as u8, client_socket_id);
                }
                AppStateActions_Action::UpdateChannelParameter if action.has_updateChannelPayload() => {
                    let payload = action.get_updateChannelPayload();
                    let channel = payload.channel as u8;
                    let source_socket_id = hash_socket_id(action.get_sourceSocketID());

                    if payload.has_volume() {
                        pianorhythm_synth::volume_change(channel, payload.get_volume() as u8, source_socket_id);
                    }

                    if payload.has_pan() {
                        pianorhythm_synth::pan_change(channel, payload.get_pan() as u8, source_socket_id);
                    }
                }
                _ => {}
            }
        }
        reduce(store, action)
    }

    fn process_effect(
        &self, _store: &crate::Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>, effect: AppStateEffects,
    ) -> Option<AppStateEffects> {
        // log::info!("Process effect: {:?}", effect);

        #[cfg(feature = "use_synth")]
        match effect.action {
            AppStateEffects_Action::DrumChannelIsMuted => {
                pianorhythm_synth::set_drum_channel_muted(effect.get_boolValue());
            }
            AppStateEffects_Action::EqualizerEnabled => {
                pianorhythm_synth::set_equalizer_enabled(effect.get_boolValue());
            }
            AppStateEffects_Action::ReverbEnabled => {
                pianorhythm_synth::synth_set_reverb(effect.get_boolValue());
            }
            AppStateEffects_Action::AudioApplyVelocityCurve => {
                pianorhythm_synth::set_apply_velocity_curve(effect.get_boolValue());
            }
            AppStateEffects_Action::SetPrimaryChannel if effect.has_uint32Value() => {
                pianorhythm_synth::set_primary_channel(effect.get_uint32Value() as u8);
            }
            // Ensure client is always added
            AppStateEffects_Action::JoinedRoomSuccess | AppStateEffects_Action::OnWelcome if _store.state().client_state.socket_id.is_some() => {
                let mut ac = AudioSynthActions::new();
                ac.set_action(AudioSynthActions_Action::AddClient);
                ac.set_socketId(_store.state().client_state.socket_id.clone().unwrap_or_default().to_string());
                _store.dispatch(create_audio_synth_action(ac));
            }
            AppStateEffects_Action::SetSlotMode if effect.has_slotMode() => pianorhythm_synth::set_slot_mode_raw(effect.get_slotMode()),
            _ => {}
        }

        Some(effect)
    }

    fn on_notify(
        &self, store: &crate::Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>, events: Vec<AppStateEvents>,
        notify: NotifyFn<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
    ) -> Vec<AppStateEvents> {
        let events = notify(store, events);

        if events.contains(&AppStateEvents::AddedClientSynthUser) {
            store.dispatch(pianorhythm_shared::util::create_action_with(AppStateActions_Action::InitializeAudioState, |_| {}));
        }

        events
    }
}
